# System Patterns & Architecture: PseudoWrite

## High-Level Architecture

- **Single HTML File SPA:** All UI, logic, and styling are packaged in a single static HTML file for maximal portability and privacy.
- **Browser-based Execution:** Relies exclusively on browser APIs—no backend, no cloud infrastructure required.

## Data Flow & Patterns

- **appState (Root Data Model):** Maintains all runtime data, serialized to `localStorage`. The critical `storyBible` section contains structured arrays of characters and world elements with required fields for each type.
- **UI State Tightly Coupled to Data Model:** Sidebar, modals, and editor reflect and mutate structured objects within `storyBible`, ensuring changes are always persistent and in sync.
- **Categorized Navigation Pattern:** Sidebar organizes characters by role and worldbuilding elements by type. Uses collapsible (accordion) sections for scalability.
- **Modal Editing Pattern:** Clicking any sidebar item opens a modal that exposes all structured fields for granular editing with clear save/delete flows.

## AI Integration

- **Prompt Engineering:** Prompts for character/worldbuilding generation and refinement are strictly structured to request full JSON objects—promotes reliable client-side parsing and consistent AI output.
- **parseAIJson Helper:** All AI completions are processed through a resilient parsing function that handles markdown/JSON mixups and parsing failures gracefully, surfacing user-friendly feedback on error.
- **Contextual AI Calls:** Writing tools (Write, Rewrite, etc.) always pass the current story context and Story Bible data to maximize result relevance.

## Implementation Sequencing

1. **Define Data Models:** Start with a robust schema for all story elements.
2. **Build UI Structure:** Layout, sidebar organization, and modal editing come next.
3. **Wire up localStorage Integration:** Automatic persistence with every data change.
4. **Integrate AI Endpoints:** Add features for generation/refinement after all scaffolding and parsing patterns are stable.

## Design Patterns & Considerations

- **No Authentication/Accounts:** Simplicity and privacy are non-negotiable (single-user, single-project MVP).
- **No Backend Dependency:** All critical logic and state remain in-browser.
- **Decoupled API Use:** The only external call is to the AI; it is stateless and does not handle sensitive user data.

---
## 2025-06-15 MVP Patterns & Enhancements

### Onboarding & Help Pattern
- Dismissible onboarding/help banner provides quick usage tips on first run and is user-toggled via a “?” button in the sidebar header.
- Onboarding/help state is persisted in localStorage (per user/dev environment).

### Error & Status Pattern
- All errors (AI, parsing, save, import/export, localStorage) are surfaced through a centralized toolbar log.
- Messages are always actionable, providing troubleshooting steps and next actions for the user.
- Success, warning, and error styles are visually distinct for clarity.

### Sidebar Quick-Add & Delete Pattern
- “+” buttons appear in Characters and Worldbuilding sidebar sections for instant item creation.
- Clicking “+” prompts for a name (and optionally role/type), creates the item, saves state, and opens the edit modal.
- Modals now support Delete actions with confirmation dialogs, ensuring safe removal of story elements.

### LocalStorage Quota Pre-Warning Pattern
- The app proactively monitors localStorage usage, warning the user in advance when nearing browser limits (~80% of 5MB).
- Users are advised to export and clear data before risking data loss.

### Automated DEV QA/Test Harness Pattern
- An embedded test suite (visible in DEV/localhost/file) provides instant QA for edge-case failures: invalid AI/JSON, storage overflow, import errors, empty-states.
- Test results are displayed in a fixed panel, supporting ongoing code safety and regression checks.

---

## Sidebar/Event Handler Durability Anti-Pattern (2025-06-15)

### Problem:
When dynamically rendering/replacing parts of the DOM (like the sidebar and its "+" quick-add buttons) in a single-page vanilla JS app, any **event handlers attached to elements on a previous render are lost** if the node is replaced. This is a standard pitfall: cached/global references to the button/node become "dead," and any event listeners added before render are never triggered.

### Solution (Implementation Pattern):

- **Always attach event handlers immediately after node creation inside the main renderSidebar() function.**
- Never cache or reference DOM elements from outside the current render cycle. Always operate on the newest, live DOM node.
- For maximum transparency, emit a debug message whenever a handler is attached or fired (see showDebugStatus added to index.html).
- Fully clear the sidebar root (`nav.innerHTML = ""`) before every rebuild to prevent duplicate/ghost handlers.
- Never attach handlers via one-time DOMContentLoaded or static markup for dynamic regions.

```js
// Example robust pattern:
const charAddBtn = document.createElement("button");
// style setup...
showDebugStatus("[ATTACH] charAddBtn handler attached at " + Date.now());
charAddBtn.addEventListener("click", () => { /* ... */ });
charHeader.appendChild(charAddBtn);
```

### Defensive Note:
If this pattern is violated:
- The UI "+" will stop responding entirely, no modal can open, and there may be **no errors in console or UI**. All modal/creation flows break invisibly. Always use handler durability tests in your QA.
- In testing, new debug messages will show handler attachment and trigger status in the debug overlay (`debug-status`), allowing instant diagnosis in any regression or complex refactor.

---

Documenting these patterns ensures that as the project grows, new features harmonize with the architecture and philosophy established from the start.
