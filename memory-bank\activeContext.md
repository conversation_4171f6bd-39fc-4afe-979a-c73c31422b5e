# Active Context: PseudoWrite

## Current Focus

- Final implementation, QA, and polish of the single-file SPA (index.html) encompassing all core product requirements.
- Ensuring end-user experience is robust, transparent, private, and self-documenting, with error resilience and onboarding for new/lapsed users.
- Automation of user-facing error/test scenarios via embedded suite for ongoing edge-case validation.
- **SIDEBAR EVENT HANDLER HARDENING:** Robust debug and architectural fix for dynamic sidebar quick-add ("+") handler loss across sidebar/sidebar re-renders.

## Recent Decisions & Changes

- **All logic/UI is now delivered in index.html, requiring no dependencies, cloud, or build steps.**
- **Onboarding and contextual help**: Banner-based quick-start guide shown at first load (with '?' button in sidebar header) helps all users discover features and recover from errors.
- **User error-resilience**: All AI (Write/Rewrite/Describe), import/export, parsing, and localStorage errors now surface user-friendly, actionable messages in the toolbar, including troubleshooting steps and how to back up or recover data.
- **Quick-add (“+”)** buttons added for Characters and Worldbuilding in the sidebar, allowing instant creation and direct modaling for full edit.
    - **(2025-06-15: Patched handler durability anti-pattern):** All "+" buttons are now attached only at point of creation in `renderSidebar`, never globally/cached. Extra debug output now shows handler attach/fire/cancel/state events in the debug overlay. Any handler loss will be instantly visible, and testability is greatly improved.
    - **Test protocol:** Add, delete, and again add via sidebar multiple times; handler fire and modal opening confirmed in every case. No handler loss or unresponsive UI can recur without visible debug/status output.
    - **Future guidance:** All dynamic DOM regions (sidebar, modal, test/QA harness UI) must ONLY attach handlers after live node creation. Reference latest update in `systemPatterns.md` for durable event patterns.
- **Delete (with confirmation)** integrated into modals, ensuring safe, transparent story element removal.
- **Automated test harness** added (DEV only) covering major edge cases (invalid input, corrupted files, quota, empty-states), enabling ongoing quality checks.
- **Accessibility**: Modal dialogs follow best practice keyboard navigation (tab-loop, ESC, visible focus), and main features are operable via keyboard. (Further ARIA/polish possible if required.)
- **LocalStorage quota warning**: Proactive notice provided if data usage exceeds 80% of common limits, prompting user export and pre-emptive management.

## What Was Built/Modified

- Full SPA: in-browser app with persistent appState, structured Story Bible, and modal sidebar architecture.
- All save/load, AI, writing/editor, and export/import logic is robust, privacy-preserving, and user-informative.
- Visual and UX improvements to onboarding, error flows, sidebar navigation, and accessibility.
- Edge-case resilience tested via automated suite embedded in the file.

## Patterns and Preferences

- Maximal transparency in error handling (always inform and suggest remedy).
- Any new feature should require zero cloud, zero additional install, and always offer visible escape routes (cancel, export, delete).
- Maintain focus on "one file, one workflow, one user" simplicity.

## Insights & Evolution

- Automated, in-app QA can quickly surface regressions or browser differences.
- User-facing guidance (onboarding, actionable errors) drastically reduces support needs and confusion.
- Preemptive storage monitoring can prevent catastrophic data loss, an essential safeguard in local-first design.
- **(2025-06-15) Lesson:** Dynamic DOM event handler loss is a silent but devastating SPA anti-pattern. Instrument with handler fire/attach debug output for immediate regression surface and add handler durability checks to all future manual and automated tests.

---

This record supersedes previous context. The application is now fully functional per MVP with all key decisions publicly surfaced to users, and maintainers have guidance for next/optional improvements (Undo/Redo, extended mobile/Safari polish, further accessibility, production bundle).
