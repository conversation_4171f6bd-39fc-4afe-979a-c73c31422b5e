# Technical Context: PseudoWrite

## Core Technologies

- **HTML/CSS/JavaScript:** All app logic, UI, and styling are included in a single HTML file for direct browser use.
- **Browser APIs:** Utilizes `localStorage` (persistence), `fetch` (AI endpoint), DOM manipulation, and event handling natively—no frameworks required.
- **Pollinations API:** Single external dependency for all AI-powered text generation and refinement.

## Dependencies

- No NPM packages, frameworks, or build tooling in MVP; source is fully client-side and highly portable.
- No backend infrastructure or external storage.

## Data Layer

- **appState** object in JavaScript memory, serializing directly to/from `localStorage`.
- **storyBible**: Structured, nested arrays of character and worldbuilding objects (see systemPatterns.md for schema details).
- **localStorage Limit:** ~5MB per origin, dictating the scale of embedded world/story data and reinforcing need for export/import feature in future.

## Integration Points

- **AI Endpoint:** `https://text.pollinations.ai/openai/v1/chat/completions`
  - Strictly stateless; only submits transient context and receives a response.
  - Fully decoupled from user/project identity.

## Technical Constraints

- Must run in a modern browser with ES6+ support and localStorage access.
- No cross-browser storage sync (per-user, per-browser session only).
- All writeable state is volatile except for localStorage.

## Development Setup

- Edit, test, and deploy by directly editing index.html—no compile step required.
- All bugfixes, UI tweaks, or new features land as incremental edits to the live HTML/JS/CSS within the file.

---
## 2025-06-15: Development QOL & QA Patterns

- **Embedded QA Harness:** index.html includes an automated DEV-only test suite for checking: error handling, storage overflow, import validation, and edge-case empty-state UI. No external runner or dependencies are needed—use localhost or file: to activate.
- **Proactive Storage Monitoring:** localStorage quota warning is implemented in JS and surfaces live UI alerts when user approaches browser limits.
- **No Build/Deploy Tooling:** All workflows—feature development, test/QA, and deployment—are performed via direct index.html editing and browser reload. No CLI, package, or web server work required for production or test.

This document underpins platform, environment, and dependency decisions for all developers contributing to PseudoWrite.
