# PseudoWrite – Project Brief

## Core Objective
PseudoWrite is a single-file, browser-based AI writing assistant created to empower creative writers with intelligent, contextually relevant assistance, while guaranteeing user privacy and requiring no installation or subscription. Its primary innovation is the "Story Bible": a structured, database-like knowledge base that deeply informs all AI-powered features.

## Goals
- Eliminate writer’s block and facilitate rich storytelling via structured context and integrated AI assistance.
- Provide writers with a tool that is intuitive, powerful, and totally private—no cloud storage, everything persists locally.
- Build an experience where AI-generated content is highly relevant, stylistically consistent, and fully personalized to each project.

## Scope & Core Requirements
- **Structured Story Bible:** Users can create, categorize, and manage all story elements (characters, worldbuilding, etc.) in an intuitive UI—with each element featuring detailed, structured fields.
- **Categorized Navigation:** Sidebar groups items by role/type in collapsible sections for clarity when browsing large stories.
- **Detail Modals:** In-depth editing for every character/world element via dedicated modal windows.
- **AI-Driven Generation and Refinement:** The AI can generate new, fully structured characters/worldbuilding elements from simple prompts as well as automatically analyze/refine details from user descriptions.
- **Integrated Writing Tools:** The main text editor supports AI-powered “Write”, “Rewrite”, and “Describe” features for direct prose assistance, all contextually aware of the Story Bible.
- **Local-First & Privacy-First:** Every bit of data stays on-device and is never sent or stored online, except for on-demand AI API usage (stateless and non-attributable).
- **Simplicity of Deployment:** The tool is shipped as a single, static HTML file using modern browser APIs and requires no setup.

## Target Users
- Fiction writers, hobbyists, NaNoWriMo participants, and worldbuilders seeking a high-quality, private creative partner that fits into their writing flow without friction.

## Out of Scope
- Multi-user collaboration (single user, single browser only at MVP)
- Cloud sync/storage, subscriptions, or user accounts
- Complex project management (MVP supports only single-project workflows)

---
This brief forms the source of truth for all planning, architecture, and implementation throughout the lifecycle of PseudoWrite.
