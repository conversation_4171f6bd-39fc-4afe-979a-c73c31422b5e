# PseudoWrite Testing Checklist
*A living QA/reference document. Check off, add, or adjust as needed with every release or significant change.*

_Last updated: 2025-06-15_

---

## E2E Flow Coverage

### Onboarding & Help
- [ ] Banner appears for new users on first load
- [ ] Dismiss or close onboarding banner works
- [ ] “?” button toggles help at any time

### Story Bible CRUD
- [ ] Add character via sidebar “+”/modal
- [ ] Add world element via sidebar “+”/modal
- [ ] **After any add, delete, or multiple cycles, all sidebar “+” buttons remain functional and open modals—handler fires are visible in debug/status output.**
- [ ] Edit character details via modal and save
- [ ] Edit world element details via modal and save
- [ ] Delete character with confirmation
- [ ] Delete world element with confirmation
- [ ] Changes persist after reload (localStorage)

### Modal/Detail Editing
- [ ] Open modal from sidebar for any item
- [ ] Save/cancel modal changes reflect in UI
- [ ] Delete actions show proper warning dialog
- [ ] <PERSON><PERSON> closes on ESC and has keyboard focus loop

### AI Features
- [ ] “Write” button invokes AI, provides result in editor
- [ ] “Rewrite” modifies selected text as expected
- [ ] “Describe” provides context prose
- [ ] Bad prompt/parse error shows clear UI error

### Data Management
- [ ] Export backup triggers valid file download
- [ ] Import backup populates app from file
- [ ] Clear/reset wipes state, shows correct empty-state UI

### Error Handling & Quota Management
- [ ] Overfull storage warns user at 80% limit
- [ ] Importing invalid/corrupt file triggers correct error flow
- [ ] AI/network/unexpected error surfaces as actionable alerts

### Accessibility
- [ ] Modals focus-trap and close on ESC
- [ ] All main operations accessible via keyboard navigation (TAB, arrows)
- [ ] Visible focus ring for interactive elements
- [ ] ARIA labeling present on modals, main panels

### Embedded DEV Harness (Edge Case)
- [ ] DEV harness tests can be run manually in DEV mode
- [ ] Results display edge-case pass/fail in-panel
- [ ] **Manual or DEV: Quickly cycle sidebar add/delete multiple times and confirm "+" handler is attached/fired after every state change. Debug overlay must show attach/fire for every test-run.**
- [ ] All critical edge-cases present: bad JSON, over-quota, import fail, empty-states

### Visual/Performance (Optional)
- [ ] Key UIs load without layout shifts or major delay
- [ ] Visual regression—major flows/screens unchanged across runs

---

> Update/expand this checklist as coverage or architecture evolves. For each release, check all items or add new relevant tests. If automating with MCP/browser driver, note any automatic coverage here for traceability.
